#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高德API路线沿途POI搜索 - 使用示例
展示各种搜索场景和功能
"""

from complete_route_poi_search import CompleteRoutePoiSearch


def example_basic_search():
    """示例1: 基础沿途搜索"""
    print("=" * 60)
    print("示例1: 基础沿途搜索 - 北京天安门到西站沿途加油站")
    print("=" * 60)
    
    # 请替换为您的API Key
    API_KEY = "your_amap_api_key_here"
    
    if API_KEY == "your_amap_api_key_here":
        print("❌ 请先配置您的高德地图API Key!")
        return
    
    searcher = CompleteRoutePoiSearch(API_KEY)
    
    result = searcher.search_along_route(
        origin="116.397428,39.90923",      # 天安门
        destination="116.322287,39.893729", # 北京西站
        keywords="加油站",
        poi_type_name="加油站",
        buffer_distance=800,
        strategy="速度优先"
    )
    
    print(searcher.format_results(result, max_display=5))


def example_batch_search():
    """示例2: 批量搜索多种POI类型"""
    print("\n" + "=" * 60)
    print("示例2: 批量搜索 - 上海外滩到虹桥机场沿途设施")
    print("=" * 60)
    
    API_KEY = "your_amap_api_key_here"
    
    if API_KEY == "your_amap_api_key_here":
        print("❌ 请先配置您的高德地图API Key!")
        return
    
    searcher = CompleteRoutePoiSearch(API_KEY)
    
    # 批量搜索多种POI类型
    poi_types = ["加油站", "餐厅", "ATM", "停车场", "医院"]
    
    batch_result = searcher.batch_search_multiple_types(
        origin="121.499763,31.245416",     # 上海外滩
        destination="121.336319,31.194382", # 虹桥机场
        poi_types=poi_types,
        buffer_distance=1000,
        strategy="速度优先"
    )
    
    if "error" in batch_result:
        print(f"❌ 批量搜索失败: {batch_result['error']}")
        return
    
    # 输出批量搜索结果
    route_info = batch_result["route_info"]
    print(f"🚗 路线信息: {route_info['distance_km']}公里, {route_info['duration_minutes']}分钟")
    print(f"🔍 搜索范围: {batch_result['search_params']['buffer_distance']}米缓冲区")
    print("\n📊 各类POI统计:")
    
    for poi_type, data in batch_result["results"].items():
        if "error" in data:
            print(f"   {poi_type}: ❌ {data['error']}")
        else:
            print(f"   {poi_type}: {data['count']}个")
            # 显示前3个结果
            for i, poi in enumerate(data["pois"][:3], 1):
                print(f"      {i}. {poi['name']} - {poi.get('address', 'N/A')}")


def example_custom_search():
    """示例3: 自定义搜索参数"""
    print("\n" + "=" * 60)
    print("示例3: 自定义搜索 - 深圳机场到市区沿途酒店")
    print("=" * 60)
    
    API_KEY = "your_amap_api_key_here"
    
    if API_KEY == "your_amap_api_key_here":
        print("❌ 请先配置您的高德地图API Key!")
        return
    
    searcher = CompleteRoutePoiSearch(API_KEY)
    
    result = searcher.search_along_route(
        origin="113.810665,22.639925",     # 深圳宝安机场
        destination="114.057868,22.543099", # 深圳市民中心
        keywords="酒店",
        poi_type_name="酒店",
        buffer_distance=1500,  # 1.5公里缓冲区
        strategy="不走高速"     # 不走高速策略
    )
    
    if result["status"] == "1":
        print("✅ 搜索成功!")
        
        # 自定义结果展示
        route = result["route_info"]
        print(f"\n🚗 路线详情:")
        print(f"   距离: {route['distance_km']}公里")
        print(f"   时间: {route['duration_minutes']}分钟")
        print(f"   过路费: {route['tolls_yuan']}元")
        print(f"   红绿灯: {route['traffic_lights']}个")
        
        print(f"\n🏨 找到 {result['total_count']} 家酒店:")
        for i, poi in enumerate(result["pois"][:8], 1):
            name = poi.get('name', 'N/A')
            address = poi.get('address', 'N/A')
            tel = poi.get('tel', '')
            print(f"   {i}. {name}")
            print(f"      📍 {address}")
            if tel:
                print(f"      📞 {tel}")
            print()
    else:
        print(f"❌ 搜索失败: {result['info']}")


def example_poi_types_demo():
    """示例4: 展示支持的POI类型"""
    print("\n" + "=" * 60)
    print("示例4: 支持的POI类型和路径策略")
    print("=" * 60)
    
    API_KEY = "your_amap_api_key_here"
    
    if API_KEY == "your_amap_api_key_here":
        print("❌ 请先配置您的高德地图API Key!")
        return
    
    searcher = CompleteRoutePoiSearch(API_KEY)
    
    # 显示支持的POI类型
    poi_types = searcher.get_available_poi_types()
    print("🎯 支持的POI类型:")
    for name, code in poi_types.items():
        print(f"   {name}: {code}")
    
    # 显示支持的路径策略
    strategies = searcher.get_available_strategies()
    print(f"\n🛣️  支持的路径策略:")
    for name, code in strategies.items():
        print(f"   {name}: {code}")


def interactive_search():
    """交互式搜索示例"""
    print("\n" + "=" * 60)
    print("交互式搜索工具")
    print("=" * 60)
    
    API_KEY = input("请输入您的高德地图API Key: ").strip()
    
    if not API_KEY:
        print("❌ API Key不能为空!")
        return
    
    try:
        searcher = CompleteRoutePoiSearch(API_KEY)
        
        print("\n请输入搜索参数:")
        origin = input("起点坐标 (格式: 经度,纬度): ").strip()
        destination = input("终点坐标 (格式: 经度,纬度): ").strip()
        keywords = input("搜索关键词: ").strip()
        
        # 显示POI类型选项
        poi_types = searcher.get_available_poi_types()
        print(f"\n可选POI类型: {', '.join(poi_types.keys())}")
        poi_type_name = input("POI类型 (可选): ").strip()
        
        # 缓冲区距离
        try:
            buffer_distance = float(input("缓冲区距离(米，默认1000): ").strip() or "1000")
        except ValueError:
            buffer_distance = 1000
        
        # 路径策略
        strategies = searcher.get_available_strategies()
        print(f"\n可选策略: {', '.join(strategies.keys())}")
        strategy = input("路径策略 (默认速度优先): ").strip() or "速度优先"
        
        if origin and destination and keywords:
            print(f"\n🔍 开始搜索...")
            result = searcher.search_along_route(
                origin=origin,
                destination=destination,
                keywords=keywords,
                poi_type_name=poi_type_name,
                buffer_distance=buffer_distance,
                strategy=strategy
            )
            
            print(searcher.format_results(result))
        else:
            print("❌ 起点、终点和关键词不能为空!")
            
    except Exception as e:
        print(f"❌ 搜索出错: {str(e)}")


def main():
    """主函数"""
    print("🗺️  高德地图沿途POI搜索 - 使用示例")
    
    while True:
        print("\n" + "=" * 50)
        print("请选择示例:")
        print("1. 基础沿途搜索")
        print("2. 批量搜索多种POI")
        print("3. 自定义搜索参数")
        print("4. 查看支持的POI类型")
        print("5. 交互式搜索")
        print("6. 退出")
        
        choice = input("\n请输入选择 (1-6): ").strip()
        
        if choice == '1':
            example_basic_search()
        elif choice == '2':
            example_batch_search()
        elif choice == '3':
            example_custom_search()
        elif choice == '4':
            example_poi_types_demo()
        elif choice == '5':
            interactive_search()
        elif choice == '6':
            print("👋 再见!")
            break
        else:
            print("❌ 无效选择，请重新输入")


if __name__ == "__main__":
    main()
