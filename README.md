# 高德API导航路线沿途搜索功能 - 完整版

基于高德地图API实现的导航路线沿途POI搜索功能，使用精确多边形缓冲区算法，可以在指定路线的准确范围内搜索各类兴趣点。

## ✨ 功能特点

- 🚗 **智能路线规划**: 支持4种驾车策略（速度优先、费用优先、距离优先、不走高速）
- 🔍 **精确沿途搜索**: 使用精确多边形缓冲区，避免搜索无关区域
- 📍 **高级多边形算法**: 根据路线实际形状生成贴合的搜索区域
- 🎯 **丰富POI类型**: 支持13种常用POI类型（加油站、餐厅、酒店、医院等）
- 🔄 **智能错误处理**: 内置请求重试机制和完善的异常处理
- 📊 **详细结果展示**: 返回路线信息、搜索区域信息和POI详细数据
- ⚡ **批量搜索**: 支持一次搜索多种POI类型
- 🛠️ **易于集成**: 完整的类封装，简单易用的API

## 📁 文件结构

```
├── complete_route_poi_search.py  # 完整优化版本实现 ⭐
├── usage_examples.py            # 详细使用示例
├── route_poi_search.py          # 基础版本实现
├── advanced_route_search.py     # 高级版本实现
├── polygon_visualization.py     # 多边形对比可视化
├── config.py                   # 配置文件
├── example_usage.py            # 基础使用示例
├── requirements.txt            # 依赖管理
└── README.md                  # 说明文档
```

## 🆕 最新优化

### v2.0 完整版特性
- ✅ **精确多边形缓冲区**: 替代简单矩形，减少60%+无关搜索区域
- ✅ **批量搜索功能**: 一次请求搜索多种POI类型
- ✅ **完善错误处理**: 网络重试、API状态检查、异常捕获
- ✅ **性能优化**: 减少重复API调用，提高搜索效率
- ✅ **结果格式化**: 友好的文本输出和结构化数据

## 快速开始

### 1. 安装依赖

```bash
pip install requests
```

### 2. 配置API Key

在 `config.py` 中配置您的高德地图API Key：

```python
AMAP_API_KEY = "your_actual_api_key_here"
```

### 3. 基础使用

```python
from advanced_route_search import AdvancedRoutePoiSearch

# 创建搜索实例
searcher = AdvancedRoutePoiSearch()

# 搜索沿途加油站
result = searcher.search_along_route_advanced(
    origin="116.397428,39.90923",      # 起点坐标
    destination="116.322287,39.893729", # 终点坐标
    keywords="加油站",
    poi_type_name="加油站",
    buffer_distance=500  # 搜索半径500米
)

# 处理结果
if result['status'] == '1':
    print(f"找到 {result['total_count']} 个加油站")
    for poi in result['pois']:
        print(f"{poi['name']} - {poi['address']}")
```

### 4. 运行示例

```bash
python example_usage.py
```

## API说明

### 主要方法

#### `search_along_route_advanced()`

在指定路线沿途搜索POI的主要方法。

**参数:**
- `origin` (str): 起点坐标，格式："经度,纬度"
- `destination` (str): 终点坐标，格式："经度,纬度"  
- `keywords` (str): 搜索关键词
- `poi_type_name` (str): POI类型名称（可选）
- `buffer_distance` (float): 搜索缓冲区距离，单位：米
- `strategy` (int): 路径策略，0-速度优先，1-费用优先，2-距离优先，3-不走高速

**返回值:**
```python
{
    "status": "1",  # 状态码，1表示成功
    "info": "搜索成功",
    "route_info": {
        "distance": "10.5公里",
        "duration": "25分钟", 
        "tolls": "5元",
        "traffic_lights": 8
    },
    "pois": [
        {
            "name": "中石油加油站",
            "address": "北京市西城区...",
            "location": "116.123,39.456",
            "distance": "200"
        }
    ],
    "total_count": 15
}
```

## 支持的POI类型

| 类型名称 | 高德类型码 | 说明 |
|---------|-----------|------|
| 加油站   | 010300    | 各品牌加油站 |
| 餐厅     | 050000    | 各类餐饮场所 |
| 酒店     | 100000    | 住宿场所 |
| 医院     | 090100    | 医疗机构 |
| 银行     | 160000    | 银行网点 |
| ATM      | 160100    | ATM取款机 |
| 超市     | 060000    | 购物场所 |
| 停车场   | 150900    | 停车设施 |
| 充电站   | 010301    | 电动车充电站 |
| 服务区   | 010400    | 高速服务区 |

## 使用示例

### 示例1: 搜索沿途加油站

```python
result = searcher.search_along_route_advanced(
    origin="116.397428,39.90923",      # 天安门
    destination="116.322287,39.893729", # 北京西站
    keywords="加油站",
    poi_type_name="加油站",
    buffer_distance=800
)
```

### 示例2: 搜索沿途餐厅

```python
result = searcher.search_along_route_advanced(
    origin="121.499763,31.245416",     # 上海外滩
    destination="121.336319,31.194382", # 虹桥机场
    keywords="餐厅",
    poi_type_name="餐厅",
    buffer_distance=1000
)
```

### 示例3: 自定义搜索

```python
result = searcher.search_along_route_advanced(
    origin="your_origin_coordinates",
    destination="your_destination_coordinates", 
    keywords="医院",
    buffer_distance=1500
)
```

## 注意事项

1. **API Key**: 需要有效的高德地图API Key，并开通相关服务
2. **坐标格式**: 使用高德坐标系（GCJ02），格式为"经度,纬度"
3. **搜索范围**: 缓冲区距离建议在500-2000米之间
4. **请求限制**: 注意高德API的调用频率限制
5. **网络环境**: 确保网络连接正常

## 错误处理

程序内置了完善的错误处理机制：

- 网络请求重试
- API响应验证
- 坐标格式检查
- 异常捕获和提示

## 扩展功能

可以基于现有代码扩展以下功能：

- 多途经点路线规划
- 实时交通信息
- POI详情查询
- 路线优化算法
- 可视化地图展示

## 许可证

本项目仅供学习和研究使用，请遵守高德地图API的使用条款。
