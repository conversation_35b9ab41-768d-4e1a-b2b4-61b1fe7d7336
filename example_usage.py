#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高德API导航路线沿途搜索功能使用示例

使用前请确保：
1. 已获取高德地图API Key
2. 在config.py中配置了正确的API Key
3. 安装了requests库: pip install requests
"""

from advanced_route_search import AdvancedRoutePoiSearch
from config import AMAP_API_KEY


def example_1_basic_search():
    """示例1: 基础沿途搜索"""
    print("示例1: 基础沿途搜索")
    print("-" * 40)
    
    searcher = AdvancedRoutePoiSearch()
    
    # 北京：从天安门到北京西站，搜索沿途加油站
    result = searcher.search_along_route_advanced(
        origin="116.397428,39.90923",      # 天安门坐标
        destination="116.322287,39.893729", # 北京西站坐标
        keywords="加油站",
        poi_type_name="加油站",
        buffer_distance=500  # 500米缓冲区
    )
    
    print_search_result(result)


def example_2_restaurant_search():
    """示例2: 搜索沿途餐厅"""
    print("\n示例2: 搜索沿途餐厅")
    print("-" * 40)
    
    searcher = AdvancedRoutePoiSearch()
    
    # 上海：从外滩到虹桥机场，搜索沿途餐厅
    result = searcher.search_along_route_advanced(
        origin="121.499763,31.245416",     # 外滩坐标
        destination="121.336319,31.194382", # 虹桥机场坐标
        keywords="餐厅",
        poi_type_name="餐厅",
        buffer_distance=1000  # 1公里缓冲区
    )
    
    print_search_result(result)


def example_3_custom_search():
    """示例3: 自定义搜索"""
    print("\n示例3: 自定义搜索")
    print("-" * 40)
    
    searcher = AdvancedRoutePoiSearch()
    
    # 自定义起终点和搜索内容
    origin = input("请输入起点坐标 (格式: 经度,纬度): ").strip()
    destination = input("请输入终点坐标 (格式: 经度,纬度): ").strip()
    keywords = input("请输入搜索关键词: ").strip()
    
    if origin and destination and keywords:
        result = searcher.search_along_route_advanced(
            origin=origin,
            destination=destination,
            keywords=keywords,
            buffer_distance=800
        )
        print_search_result(result)
    else:
        print("输入信息不完整")


def example_4_multiple_poi_types():
    """示例4: 搜索多种类型POI"""
    print("\n示例4: 搜索多种类型POI")
    print("-" * 40)
    
    searcher = AdvancedRoutePoiSearch()
    
    # 同一路线搜索多种POI
    origin = "116.397428,39.90923"      # 天安门
    destination = "116.322287,39.893729" # 北京西站
    
    poi_types = ["加油站", "餐厅", "ATM", "停车场"]
    
    for poi_type in poi_types:
        print(f"\n🔍 搜索沿途{poi_type}:")
        result = searcher.search_along_route_advanced(
            origin=origin,
            destination=destination,
            keywords=poi_type,
            poi_type_name=poi_type,
            buffer_distance=600
        )
        
        if result['status'] == '1':
            print(f"找到 {result['total_count']} 个{poi_type}")
            # 只显示前3个结果
            top_pois = result['pois'][:3]
            for i, poi in enumerate(top_pois, 1):
                print(f"  {i}. {poi['name']} - {poi.get('address', 'N/A')}")
        else:
            print(f"搜索失败: {result['info']}")


def print_search_result(result):
    """打印搜索结果"""
    if result['status'] == '1':
        print("✅ 搜索成功!")
        
        # 路线信息
        print("\n📊 路线信息:")
        route_info = result['route_info']
        print(f"  距离: {route_info['distance']}")
        print(f"  时间: {route_info['duration']}")
        if 'tolls' in route_info:
            print(f"  过路费: {route_info['tolls']}")
        
        # POI结果
        print(f"\n🎯 找到 {result['total_count']} 个相关POI:")
        
        searcher = AdvancedRoutePoiSearch()
        formatted_result = searcher.format_poi_results(result['pois'], 5)
        print(formatted_result)
        
    else:
        print(f"❌ 搜索失败: {result['info']}")


def interactive_search():
    """交互式搜索"""
    print("🗺️  高德地图沿途POI搜索工具")
    print("=" * 50)
    
    if AMAP_API_KEY == "your_amap_api_key_here":
        print("⚠️  请先在config.py中配置您的高德地图API Key!")
        return
    
    searcher = AdvancedRoutePoiSearch()
    
    while True:
        print("\n请选择操作:")
        print("1. 基础搜索示例")
        print("2. 餐厅搜索示例") 
        print("3. 自定义搜索")
        print("4. 多类型POI搜索")
        print("5. 退出")
        
        choice = input("\n请输入选择 (1-5): ").strip()
        
        if choice == '1':
            example_1_basic_search()
        elif choice == '2':
            example_2_restaurant_search()
        elif choice == '3':
            example_3_custom_search()
        elif choice == '4':
            example_4_multiple_poi_types()
        elif choice == '5':
            print("👋 再见!")
            break
        else:
            print("❌ 无效选择，请重新输入")


if __name__ == "__main__":
    interactive_search()
