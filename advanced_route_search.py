import requests
import json
import math
import time
from typing import List, Tuple, Dict, Any, Optional
from config import AMAP_API_KEY, DEFAULT_BUFFER_DISTANCE, POI_TYPES, REQUEST_CONFIG


class AdvancedRoutePoiSearch:
    """高级版本的路线沿途POI搜索类"""
    
    def __init__(self, api_key: str = AMAP_API_KEY):
        self.api_key = api_key
        self.base_url = "https://restapi.amap.com"
        self.session = requests.Session()
        
    def _make_request(self, url: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        发送HTTP请求，包含重试机制
        """
        params['key'] = self.api_key
        
        for attempt in range(REQUEST_CONFIG['max_retries']):
            try:
                response = self.session.get(
                    url, 
                    params=params, 
                    timeout=REQUEST_CONFIG['timeout']
                )
                response.raise_for_status()
                return response.json()
                
            except requests.RequestException as e:
                if attempt == REQUEST_CONFIG['max_retries'] - 1:
                    return {"status": "0", "info": f"请求失败: {str(e)}"}
                time.sleep(REQUEST_CONFIG['retry_delay'])
        
        return {"status": "0", "info": "请求失败"}
    
    def get_route_with_steps(self, origin: str, destination: str, 
                           strategy: int = 0) -> Dict[str, Any]:
        """
        获取详细的路线规划，包含每个路段信息
        :param origin: 起点坐标
        :param destination: 终点坐标  
        :param strategy: 路径策略 0-速度优先 1-费用优先 2-距离优先 3-不走高速
        """
        url = f"{self.base_url}/v3/direction/driving"
        params = {
            'origin': origin,
            'destination': destination,
            'extensions': 'all',
            'strategy': strategy,
            'waypoints': '',  # 可以添加途经点
        }
        
        return self._make_request(url, params)
    
    def create_precise_buffer_polygon(self, coordinates: List[Tuple[float, float]], 
                                    buffer_distance: float) -> List[Tuple[float, float]]:
        """
        创建更精确的缓冲区多边形
        """
        if len(coordinates) < 2:
            return []
        
        # 分段处理，为每段路线创建缓冲区
        all_buffer_points = []
        
        for i in range(len(coordinates) - 1):
            segment_buffer = self._create_segment_buffer(
                coordinates[i], coordinates[i + 1], buffer_distance
            )
            all_buffer_points.extend(segment_buffer)
        
        # 使用凸包算法或简化算法处理重叠区域
        return self._simplify_polygon(all_buffer_points)
    
    def _create_segment_buffer(self, point1: Tuple[float, float], 
                             point2: Tuple[float, float], 
                             buffer_distance: float) -> List[Tuple[float, float]]:
        """为单个路段创建缓冲区"""
        lng1, lat1 = point1
        lng2, lat2 = point2
        
        # 计算方向向量
        dx = lng2 - lng1
        dy = lat2 - lat1
        length = math.sqrt(dx*dx + dy*dy)
        
        if length == 0:
            return []
        
        # 计算偏移量（考虑地球曲率）
        lat_offset = buffer_distance / 111000
        lng_offset = buffer_distance / (111000 * math.cos(math.radians((lat1 + lat2) / 2)))
        
        # 计算垂直向量
        perp_x = -dy / length * lng_offset
        perp_y = dx / length * lat_offset
        
        # 返回矩形的四个角点
        return [
            (lng1 + perp_x, lat1 + perp_y),
            (lng1 - perp_x, lat1 - perp_y),
            (lng2 - perp_x, lat2 - perp_y),
            (lng2 + perp_x, lat2 + perp_y)
        ]
    
    def _simplify_polygon(self, points: List[Tuple[float, float]]) -> List[Tuple[float, float]]:
        """简化多边形，返回边界框"""
        if not points:
            return []
        
        min_lng = min(p[0] for p in points)
        max_lng = max(p[0] for p in points)
        min_lat = min(p[1] for p in points)
        max_lat = max(p[1] for p in points)
        
        return [
            (min_lng, min_lat),
            (max_lng, min_lat),
            (max_lng, max_lat),
            (min_lng, max_lat),
            (min_lng, min_lat)
        ]
    
    def search_poi_in_polygon(self, polygon: List[Tuple[float, float]], 
                            keywords: str, poi_type: str = "", 
                            page_size: int = 20, page_num: int = 1) -> Dict[str, Any]:
        """在多边形内搜索POI"""
        polygon_str = "|".join([f"{lng},{lat}" for lng, lat in polygon])
        
        url = f"{self.base_url}/v3/place/polygon"
        params = {
            'polygon': polygon_str,
            'keywords': keywords,
            'extensions': 'all',
            'page_size': page_size,
            'page_num': page_num
        }
        
        if poi_type:
            params['types'] = poi_type
        
        return self._make_request(url, params)
    
    def search_along_route_advanced(self, origin: str, destination: str,
                                  keywords: str, poi_type_name: str = "",
                                  buffer_distance: float = DEFAULT_BUFFER_DISTANCE,
                                  strategy: int = 0) -> Dict[str, Any]:
        """
        高级沿途搜索功能
        """
        result = {
            "status": "0",
            "info": "",
            "route_info": {},
            "search_params": {
                "keywords": keywords,
                "poi_type": poi_type_name,
                "buffer_distance": buffer_distance,
                "strategy": strategy
            },
            "pois": [],
            "total_count": 0
        }
        
        try:
            # 1. 获取路线
            print("🚗 获取路线规划...")
            route_data = self.get_route_with_steps(origin, destination, strategy)
            
            if route_data.get('status') != '1':
                result["info"] = f"路线规划失败: {route_data.get('info')}"
                return result
            
            # 2. 解析路线信息
            route = route_data['route']
            path = route['paths'][0]
            
            result["route_info"] = {
                "distance": f"{float(path['distance'])/1000:.1f}公里",
                "duration": f"{int(path['duration'])//60}分钟",
                "tolls": f"{path.get('tolls', 0)}元",
                "traffic_lights": path.get('traffic_lights', 0)
            }
            
            # 3. 提取坐标点
            coordinates = []
            for step in path['steps']:
                polyline = step['polyline']
                coords = [tuple(map(float, coord.split(','))) 
                         for coord in polyline.split(';') if coord]
                coordinates.extend(coords)
            
            print(f"📍 解析到 {len(coordinates)} 个路线坐标点")
            
            # 4. 创建搜索多边形
            print(f"🔍 创建 {buffer_distance}米 搜索缓冲区...")
            polygon = self.create_precise_buffer_polygon(coordinates, buffer_distance)
            
            if not polygon:
                result["info"] = "无法创建搜索区域"
                return result
            
            # 5. 确定POI类型
            poi_type = POI_TYPES.get(poi_type_name, "")
            
            # 6. 搜索POI
            print(f"🎯 搜索关键词: {keywords}")
            poi_result = self.search_poi_in_polygon(polygon, keywords, poi_type)
            
            if poi_result.get('status') == '1':
                result["status"] = "1"
                result["info"] = "搜索成功"
                result["pois"] = poi_result.get('pois', [])
                result["total_count"] = int(poi_result.get('count', 0))
                
                print(f"✅ 找到 {result['total_count']} 个相关POI")
            else:
                result["info"] = f"POI搜索失败: {poi_result.get('info')}"
            
        except Exception as e:
            result["info"] = f"搜索过程出错: {str(e)}"
        
        return result
    
    def format_poi_results(self, pois: List[Dict], max_display: int = 10) -> str:
        """格式化POI搜索结果"""
        if not pois:
            return "未找到相关POI"
        
        output = []
        for i, poi in enumerate(pois[:max_display], 1):
            name = poi.get('name', 'N/A')
            address = poi.get('address', 'N/A')
            location = poi.get('location', 'N/A')
            distance = poi.get('distance', 'N/A')
            
            output.append(f"{i}. {name}")
            output.append(f"   📍 {address}")
            output.append(f"   🗺️  坐标: {location}")
            if distance != 'N/A':
                output.append(f"   📏 距离: {distance}米")
            output.append("")
        
        if len(pois) > max_display:
            output.append(f"... 还有 {len(pois) - max_display} 个结果")
        
        return "\n".join(output)


def demo_search():
    """演示搜索功能"""
    searcher = AdvancedRoutePoiSearch()
    
    # 示例搜索参数
    examples = [
        {
            "name": "北京天安门到西站沿途加油站",
            "origin": "116.397428,39.90923",
            "destination": "116.322287,39.893729", 
            "keywords": "加油站",
            "poi_type": "加油站",
            "buffer": 800
        },
        {
            "name": "上海外滩到虹桥机场沿途餐厅",
            "origin": "121.499763,31.245416",
            "destination": "121.336319,31.194382",
            "keywords": "餐厅",
            "poi_type": "餐厅", 
            "buffer": 1000
        }
    ]
    
    for example in examples:
        print(f"\n{'='*50}")
        print(f"🔍 {example['name']}")
        print(f"{'='*50}")
        
        result = searcher.search_along_route_advanced(
            origin=example['origin'],
            destination=example['destination'],
            keywords=example['keywords'],
            poi_type_name=example['poi_type'],
            buffer_distance=example['buffer']
        )
        
        if result['status'] == '1':
            print(f"📊 路线信息:")
            for key, value in result['route_info'].items():
                print(f"   {key}: {value}")
            
            print(f"\n🎯 搜索结果:")
            print(searcher.format_poi_results(result['pois']))
        else:
            print(f"❌ {result['info']}")


if __name__ == "__main__":
    demo_search()
