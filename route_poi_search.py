import requests
import json
import math
from typing import List, Tuple, Dict, Any


class AMapRoutePoiSearch:
    """高德地图导航路线沿途POI搜索类"""
    
    def __init__(self, api_key: str):
        """
        初始化
        :param api_key: 高德地图API密钥
        """
        self.api_key = api_key
        self.base_url = "https://restapi.amap.com/v3"
    
    def get_driving_route(self, origin: str, destination: str) -> Dict[str, Any]:
        """
        获取驾车路线规划
        :param origin: 起点坐标 "经度,纬度"
        :param destination: 终点坐标 "经度,纬度"
        :return: 路线规划结果
        """
        url = f"{self.base_url}/direction/driving"
        params = {
            'key': self.api_key,
            'origin': origin,
            'destination': destination,
            'extensions': 'all'  # 返回详细路径信息
        }
        
        response = requests.get(url, params=params)
        return response.json()
    
    def parse_route_coordinates(self, route_data: Dict[str, Any]) -> List[Tuple[float, float]]:
        """
        解析路线坐标点
        :param route_data: 路线规划数据
        :return: 坐标点列表 [(经度, 纬度), ...]
        """
        coordinates = []
        
        if route_data.get('status') == '1' and route_data.get('route'):
            paths = route_data['route']['paths']
            for path in paths:
                for step in path['steps']:
                    polyline = step['polyline']
                    # 解析polyline坐标字符串
                    coord_pairs = polyline.split(';')
                    for coord_pair in coord_pairs:
                        if coord_pair:
                            lng, lat = map(float, coord_pair.split(','))
                            coordinates.append((lng, lat))
        
        return coordinates
    
    def create_buffer_polygon(self, coordinates: List[Tuple[float, float]], 
                            buffer_distance: float = 1000) -> List[Tuple[float, float]]:
        """
        为路线创建缓冲区多边形
        :param coordinates: 路线坐标点
        :param buffer_distance: 缓冲区距离（米）
        :return: 多边形坐标点
        """
        if len(coordinates) < 2:
            return []
        
        # 简化版本：为每个路段创建矩形缓冲区
        polygon_points = []
        
        # 将米转换为大约的经纬度偏移量（粗略计算）
        lat_offset = buffer_distance / 111000  # 1度纬度约111km
        
        for i in range(len(coordinates) - 1):
            lng1, lat1 = coordinates[i]
            lng2, lat2 = coordinates[i + 1]
            
            # 计算该点的经度偏移量（考虑纬度影响）
            lng_offset = buffer_distance / (111000 * math.cos(math.radians(lat1)))
            
            # 计算垂直于路段方向的偏移向量
            dx = lng2 - lng1
            dy = lat2 - lat1
            length = math.sqrt(dx*dx + dy*dy)
            
            if length > 0:
                # 单位垂直向量
                perp_x = -dy / length * lng_offset
                perp_y = dx / length * lat_offset
                
                # 添加缓冲区的四个角点
                polygon_points.extend([
                    (lng1 + perp_x, lat1 + perp_y),
                    (lng1 - perp_x, lat1 - perp_y),
                    (lng2 - perp_x, lat2 - perp_y),
                    (lng2 + perp_x, lat2 + perp_y)
                ])
        
        # 使用凸包算法简化多边形（这里使用简单的边界框）
        if polygon_points:
            min_lng = min(p[0] for p in polygon_points)
            max_lng = max(p[0] for p in polygon_points)
            min_lat = min(p[1] for p in polygon_points)
            max_lat = max(p[1] for p in polygon_points)
            
            # 返回边界框的四个角点
            return [
                (min_lng, min_lat),
                (max_lng, min_lat),
                (max_lng, max_lat),
                (min_lng, max_lat),
                (min_lng, min_lat)  # 闭合多边形
            ]
        
        return []
    
    def polygon_search_poi(self, polygon: List[Tuple[float, float]], 
                          keywords: str, poi_type: str = "") -> Dict[str, Any]:
        """
        在多边形区域内搜索POI
        :param polygon: 多边形坐标点列表
        :param keywords: 搜索关键词
        :param poi_type: POI类型
        :return: 搜索结果
        """
        if len(polygon) < 3:
            return {"status": "0", "info": "多边形坐标点不足"}
        
        # 将多边形坐标转换为字符串格式
        polygon_str = "|".join([f"{lng},{lat}" for lng, lat in polygon])
        
        url = f"{self.base_url}/place/polygon"
        params = {
            'key': self.api_key,
            'polygon': polygon_str,
            'keywords': keywords,
            'extensions': 'all'
        }
        
        if poi_type:
            params['types'] = poi_type
        
        response = requests.get(url, params=params)
        return response.json()
    
    def search_along_route(self, origin: str, destination: str, 
                          keywords: str, poi_type: str = "", 
                          buffer_distance: float = 1000) -> Dict[str, Any]:
        """
        沿路线搜索POI的主要方法
        :param origin: 起点坐标
        :param destination: 终点坐标
        :param keywords: 搜索关键词
        :param poi_type: POI类型
        :param buffer_distance: 搜索缓冲区距离（米）
        :return: 搜索结果
        """
        # 1. 获取路线规划
        print("正在获取路线规划...")
        route_data = self.get_driving_route(origin, destination)
        
        if route_data.get('status') != '1':
            return {
                "status": "0",
                "info": f"路线规划失败: {route_data.get('info', '未知错误')}"
            }
        
        # 2. 解析路线坐标
        print("正在解析路线坐标...")
        coordinates = self.parse_route_coordinates(route_data)
        
        if not coordinates:
            return {"status": "0", "info": "无法解析路线坐标"}
        
        print(f"解析到 {len(coordinates)} 个路线坐标点")
        
        # 3. 创建缓冲区多边形
        print(f"正在创建 {buffer_distance}米 缓冲区...")
        polygon = self.create_buffer_polygon(coordinates, buffer_distance)
        
        if not polygon:
            return {"status": "0", "info": "无法创建缓冲区多边形"}
        
        print(f"创建了包含 {len(polygon)} 个顶点的多边形")
        
        # 4. 在多边形区域内搜索POI
        print(f"正在搜索关键词: {keywords}")
        poi_result = self.polygon_search_poi(polygon, keywords, poi_type)
        
        # 5. 整合结果
        result = {
            "status": poi_result.get("status"),
            "info": poi_result.get("info"),
            "route_info": {
                "distance": route_data['route']['paths'][0]['distance'],
                "duration": route_data['route']['paths'][0]['duration'],
                "coordinate_count": len(coordinates)
            },
            "search_area": {
                "polygon": polygon,
                "buffer_distance": buffer_distance
            },
            "pois": poi_result.get("pois", []),
            "count": poi_result.get("count", 0)
        }
        
        return result


def main():
    """示例使用"""
    # 请替换为您的高德地图API Key
    API_KEY = "your_amap_api_key_here"
    
    # 创建搜索实例
    searcher = AMapRoutePoiSearch(API_KEY)
    
    # 示例：从北京天安门到北京西站沿途搜索加油站
    origin = "116.397428,39.90923"      # 天安门
    destination = "116.322287,39.893729"  # 北京西站
    keywords = "加油站"
    buffer_distance = 500  # 500米缓冲区
    
    print("开始沿途搜索...")
    result = searcher.search_along_route(
        origin=origin,
        destination=destination,
        keywords=keywords,
        buffer_distance=buffer_distance
    )
    
    # 输出结果
    if result["status"] == "1":
        print(f"\n搜索成功！")
        print(f"路线距离: {result['route_info']['distance']}米")
        print(f"预计时间: {result['route_info']['duration']}秒")
        print(f"找到 {result['count']} 个相关POI:")
        
        for i, poi in enumerate(result["pois"][:5], 1):  # 只显示前5个
            print(f"{i}. {poi['name']} - {poi['address']}")
            print(f"   坐标: {poi['location']}")
            print(f"   类型: {poi.get('type', 'N/A')}")
            print()
    else:
        print(f"搜索失败: {result['info']}")


if __name__ == "__main__":
    main()
