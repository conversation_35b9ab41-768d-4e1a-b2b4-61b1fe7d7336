#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多边形缓冲区可视化示例
展示新的精确多边形生成与旧的矩形边界框的区别
"""

import math
from typing import List, Tuple
import json


class PolygonComparison:
    """多边形生成方法对比"""
    
    def create_precise_polygon(self, coordinates: List[Tuple[float, float]], 
                             buffer_distance: float) -> List[Tuple[float, float]]:
        """新方法：创建精确的缓冲区多边形"""
        if len(coordinates) < 2:
            return []
        
        left_points = []   # 路线左侧的点
        right_points = []  # 路线右侧的点
        
        for i in range(len(coordinates) - 1):
            lng1, lat1 = coordinates[i]
            lng2, lat2 = coordinates[i + 1]
            
            # 计算偏移量
            lat_offset = buffer_distance / 111000
            lng_offset = buffer_distance / (111000 * math.cos(math.radians((lat1 + lat2) / 2)))
            
            # 计算方向向量和垂直向量
            dx = lng2 - lng1
            dy = lat2 - lat1
            length = math.sqrt(dx*dx + dy*dy)
            
            if length > 0:
                # 单位垂直向量
                perp_x = -dy / length * lng_offset
                perp_y = dx / length * lat_offset
                
                # 添加左右两侧的点
                left_points.append((lng1 + perp_x, lat1 + perp_y))
                right_points.append((lng1 - perp_x, lat1 - perp_y))
        
        # 添加最后一个点的左右两侧
        if coordinates:
            last_lng, last_lat = coordinates[-1]
            if len(coordinates) >= 2:
                prev_lng, prev_lat = coordinates[-2]
                dx = last_lng - prev_lng
                dy = last_lat - prev_lat
                length = math.sqrt(dx*dx + dy*dy)
                
                if length > 0:
                    lat_offset = buffer_distance / 111000
                    lng_offset = buffer_distance / (111000 * math.cos(math.radians(last_lat)))
                    perp_x = -dy / length * lng_offset
                    perp_y = dx / length * lat_offset
                    
                    left_points.append((last_lng + perp_x, last_lat + perp_y))
                    right_points.append((last_lng - perp_x, last_lat - perp_y))
        
        # 构建完整的多边形：左侧点 + 反向的右侧点
        polygon = left_points + right_points[::-1]
        
        # 闭合多边形
        if polygon and polygon[0] != polygon[-1]:
            polygon.append(polygon[0])
        
        return polygon
    
    def create_simple_rectangle(self, coordinates: List[Tuple[float, float]], 
                              buffer_distance: float) -> List[Tuple[float, float]]:
        """旧方法：创建简单的矩形边界框"""
        if not coordinates:
            return []
        
        # 计算所有点的边界
        min_lng = min(p[0] for p in coordinates)
        max_lng = max(p[0] for p in coordinates)
        min_lat = min(p[1] for p in coordinates)
        max_lat = max(p[1] for p in coordinates)
        
        # 添加缓冲区
        lat_offset = buffer_distance / 111000
        lng_offset = buffer_distance / (111000 * math.cos(math.radians((min_lat + max_lat) / 2)))
        
        return [
            (min_lng - lng_offset, min_lat - lat_offset),
            (max_lng + lng_offset, min_lat - lat_offset),
            (max_lng + lng_offset, max_lat + lat_offset),
            (min_lng - lng_offset, max_lat + lat_offset),
            (min_lng - lng_offset, min_lat - lat_offset)
        ]
    
    def calculate_polygon_area(self, polygon: List[Tuple[float, float]]) -> float:
        """计算多边形面积（平方度）"""
        if len(polygon) < 3:
            return 0
        
        area = 0
        for i in range(len(polygon) - 1):
            area += polygon[i][0] * polygon[i + 1][1]
            area -= polygon[i + 1][0] * polygon[i][1]
        
        return abs(area) / 2
    
    def compare_polygons(self, coordinates: List[Tuple[float, float]], 
                        buffer_distance: float) -> dict:
        """对比两种多边形生成方法"""
        
        # 生成两种多边形
        precise_polygon = self.create_precise_polygon(coordinates, buffer_distance)
        simple_rectangle = self.create_simple_rectangle(coordinates, buffer_distance)
        
        # 计算面积
        precise_area = self.calculate_polygon_area(precise_polygon)
        rectangle_area = self.calculate_polygon_area(simple_rectangle)
        
        # 计算顶点数量
        precise_vertices = len(precise_polygon) - 1  # 减去闭合点
        rectangle_vertices = len(simple_rectangle) - 1
        
        return {
            "route_points": len(coordinates),
            "buffer_distance": buffer_distance,
            "precise_polygon": {
                "vertices": precise_vertices,
                "area": precise_area,
                "coordinates": precise_polygon
            },
            "simple_rectangle": {
                "vertices": rectangle_vertices, 
                "area": rectangle_area,
                "coordinates": simple_rectangle
            },
            "area_ratio": rectangle_area / precise_area if precise_area > 0 else 0,
            "efficiency_gain": f"{((rectangle_area - precise_area) / rectangle_area * 100):.1f}%" if rectangle_area > 0 else "0%"
        }


def demo_polygon_comparison():
    """演示多边形对比"""
    comparator = PolygonComparison()
    
    # 示例路线：模拟一条弯曲的路线
    test_routes = [
        {
            "name": "直线路线",
            "coordinates": [
                (116.397428, 39.90923),   # 起点
                (116.407428, 39.91923),   # 终点
            ]
        },
        {
            "name": "L型路线", 
            "coordinates": [
                (116.397428, 39.90923),   # 起点
                (116.407428, 39.90923),   # 中间点
                (116.407428, 39.91923),   # 终点
            ]
        },
        {
            "name": "弯曲路线",
            "coordinates": [
                (116.397428, 39.90923),   # 起点
                (116.400428, 39.90823),   # 点1
                (116.403428, 39.90923),   # 点2
                (116.406428, 39.91023),   # 点3
                (116.407428, 39.91923),   # 终点
            ]
        },
        {
            "name": "复杂路线",
            "coordinates": [
                (116.397428, 39.90923),   # 起点
                (116.399428, 39.90823),   # 点1
                (116.401428, 39.90923),   # 点2
                (116.403428, 39.90823),   # 点3
                (116.405428, 39.90923),   # 点4
                (116.407428, 39.91023),   # 点5
                (116.409428, 39.91123),   # 点6
                (116.407428, 39.91923),   # 终点
            ]
        }
    ]
    
    buffer_distances = [500, 1000, 1500]
    
    print("🔍 多边形生成方法对比分析")
    print("=" * 60)
    
    for route in test_routes:
        print(f"\n📍 {route['name']} (包含 {len(route['coordinates'])} 个点)")
        print("-" * 40)
        
        for buffer_dist in buffer_distances:
            result = comparator.compare_polygons(route['coordinates'], buffer_dist)
            
            print(f"\n缓冲区距离: {buffer_dist}米")
            print(f"  精确多边形: {result['precise_polygon']['vertices']} 个顶点")
            print(f"  简单矩形:   {result['simple_rectangle']['vertices']} 个顶点")
            print(f"  面积比例:   {result['area_ratio']:.2f} (矩形/精确)")
            print(f"  效率提升:   {result['efficiency_gain']}")
    
    # 详细展示一个例子
    print(f"\n{'='*60}")
    print("📊 详细坐标对比示例")
    print(f"{'='*60}")
    
    example_route = test_routes[2]['coordinates']  # 弯曲路线
    example_buffer = 1000
    
    result = comparator.compare_polygons(example_route, example_buffer)
    
    print(f"\n原始路线坐标 ({len(example_route)} 个点):")
    for i, coord in enumerate(example_route):
        print(f"  {i+1}. {coord}")
    
    print(f"\n精确多边形坐标 ({result['precise_polygon']['vertices']} 个顶点):")
    for i, coord in enumerate(result['precise_polygon']['coordinates'][:10]):  # 只显示前10个
        print(f"  {i+1}. ({coord[0]:.6f}, {coord[1]:.6f})")
    if len(result['precise_polygon']['coordinates']) > 10:
        print(f"  ... 还有 {len(result['precise_polygon']['coordinates']) - 10} 个顶点")
    
    print(f"\n简单矩形坐标 ({result['simple_rectangle']['vertices']} 个顶点):")
    for i, coord in enumerate(result['simple_rectangle']['coordinates']):
        print(f"  {i+1}. ({coord[0]:.6f}, {coord[1]:.6f})")
    
    # 保存结果到文件
    with open('polygon_comparison_result.json', 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 详细结果已保存到 polygon_comparison_result.json")


def explain_polygon_benefits():
    """解释精确多边形的优势"""
    print("\n🎯 精确多边形 vs 简单矩形")
    print("=" * 50)
    
    benefits = [
        "✅ 更精确的搜索范围 - 减少无关POI",
        "✅ 贴合实际路线形状 - 避免搜索到偏远区域",
        "✅ 更好的用户体验 - 搜索结果更相关",
        "✅ 节省API调用成本 - 减少无效数据传输",
        "✅ 支持复杂路线 - 适应弯曲、转折路线"
    ]
    
    drawbacks = [
        "⚠️ 计算复杂度稍高 - 需要更多计算",
        "⚠️ 顶点数量增加 - 多边形更复杂",
        "⚠️ 内存占用稍大 - 存储更多坐标点"
    ]
    
    print("优势:")
    for benefit in benefits:
        print(f"  {benefit}")
    
    print("\n注意事项:")
    for drawback in drawbacks:
        print(f"  {drawback}")
    
    print("\n💡 建议:")
    print("  • 对于简单直线路线，两种方法差异不大")
    print("  • 对于复杂弯曲路线，精确多边形明显更优")
    print("  • 可根据路线复杂度动态选择方法")


if __name__ == "__main__":
    demo_polygon_comparison()
    explain_polygon_benefits()
